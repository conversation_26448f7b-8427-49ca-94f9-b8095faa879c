# Panduan Setup dan Development MyBinder

## 🚀 Quick Start Guide

### **Prerequisites**
Pastikan sistem Anda memiliki:
- **Node.js 18+** (Recommended: 20.x LTS)
- **Docker & Docker Compose** untuk database lokal
- **Git** untuk version control
- **VS Code** (Recommended) dengan extensions:
  - TypeScript and JavaScript Language Features
  - Prisma
  - Tailwind CSS IntelliSense
  - ESLint

### **1. Repository Setup**
```bash
# Clone repository
git clone https://github.com/Pepryan/mybinder.git
cd mybinder

# Install dependencies
npm install

# Verify installation
npm run --version
```

### **2. Environment Configuration**
```bash
# Copy environment template
cp .env.example .env.local

# Edit environment variables
nano .env.local
```

**Required Environment Variables:**
```env
# Database Configuration
DATABASE_URL="postgresql://postgres:postgres@localhost:5432/mybinder"

# JWT Authentication
JWT_SECRET="your-super-secret-jwt-key-minimum-32-characters"

# Next.js Configuration
NEXTAUTH_SECRET="your-nextauth-secret-key-minimum-32-characters"
NEXTAUTH_URL="http://localhost:3000"

# Optional: Development flags
NODE_ENV="development"
```

### **3. Database Setup**
```bash
# Start PostgreSQL dengan Docker
docker-compose up -d

# Verify database is running
docker ps

# Generate Prisma client
npx prisma generate

# Push schema ke database
npx prisma db push

# Seed database dengan demo data
npm run db:seed

# (Optional) Open Prisma Studio
npx prisma studio
```

### **4. Development Server**
```bash
# Start development server dengan Turbopack
npm run dev

# Server akan berjalan di http://localhost:3000
```

## 🛠️ Development Workflow

### **Daily Development Routine**

#### **Morning Setup**
```bash
# Pull latest changes
git pull origin main

# Install new dependencies (jika ada)
npm install

# Check database status
docker ps

# Start development
npm run dev
```

#### **Code Development**
```bash
# Create feature branch
git checkout -b feature/your-feature-name

# Make changes...

# Run tests
npm test

# Run linting
npm run lint

# Fix linting issues
npm run lint:fix
```

#### **Before Commit**
```bash
# Run full test suite
npm run test:coverage

# Check TypeScript compilation
npm run build

# Commit changes
git add .
git commit -m "feat: add your feature description"
```

### **Database Development**

#### **Schema Changes**
```bash
# Edit prisma/schema.prisma
# Then push changes
npx prisma db push

# Generate new client
npx prisma generate

# Reset database (if needed)
npm run db:reset
```

#### **Seeding Data**
```bash
# Run seed script
npm run db:seed

# Or manually with tsx
npx tsx prisma/seed.ts
```

#### **Database Inspection**
```bash
# Open Prisma Studio (GUI)
npx prisma studio

# Or connect dengan psql
docker exec -it mybinder-postgres psql -U postgres -d mybinder
```

## 🧪 Testing Development

### **Running Tests**

#### **Unit Tests**
```bash
# Run all tests
npm test

# Run tests dalam watch mode
npm run test:watch

# Run dengan coverage
npm run test:coverage

# Run specific test file
npm test -- --testPathPattern=auth
```

#### **E2E Tests**
```bash
# Install Playwright browsers (first time)
npx playwright install

# Run E2E tests
npx playwright test

# Run dengan UI mode
npx playwright test --ui

# Run specific test
npx playwright test tests/basic.spec.ts
```

#### **Mobile Testing**
```bash
# Run mobile-specific tests
npx playwright test tests/mobile-ui.spec.ts

# Test dengan different viewports
npx playwright test --project="Mobile Chrome"
```

### **Writing Tests**

#### **Unit Test Example**
```typescript
// src/__tests__/lib/auth.test.ts
import { hashPassword, verifyPassword } from '@/lib/auth'

describe('Auth utilities', () => {
  it('should hash and verify password correctly', async () => {
    const password = 'testpassword123'
    const hashed = await hashPassword(password)
    
    expect(hashed).not.toBe(password)
    expect(await verifyPassword(password, hashed)).toBe(true)
  })
})
```

#### **API Test Example**
```typescript
// src/__tests__/api/auth.test.ts
import { createMocks } from 'node-mocks-http'
import handler from '@/app/api/auth/login/route'

describe('/api/auth/login', () => {
  it('should login with valid credentials', async () => {
    const { req, res } = createMocks({
      method: 'POST',
      body: {
        email: '<EMAIL>',
        password: 'demo123'
      }
    })

    await handler(req, res)
    expect(res._getStatusCode()).toBe(200)
  })
})
```

## 🔧 Development Tools

### **VS Code Configuration**

#### **Recommended Extensions**
```json
// .vscode/extensions.json
{
  "recommendations": [
    "bradlc.vscode-tailwindcss",
    "prisma.prisma",
    "esbenp.prettier-vscode",
    "ms-vscode.vscode-typescript-next"
  ]
}
```

#### **Workspace Settings**
```json
// .vscode/settings.json
{
  "typescript.preferences.importModuleSpecifier": "relative",
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "tailwindCSS.experimental.classRegex": [
    ["clsx\\(([^)]*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"]
  ]
}
```

### **Git Workflow**

#### **Branch Naming Convention**
```bash
feature/add-user-authentication
bugfix/fix-message-threading
hotfix/security-patch
refactor/improve-database-queries
```

#### **Commit Message Convention**
```bash
feat: add user authentication system
fix: resolve message threading issue
docs: update API documentation
style: format code with prettier
refactor: optimize database queries
test: add unit tests for auth module
```

### **Code Quality Tools**

#### **ESLint Configuration**
```bash
# Run linting
npm run lint

# Fix auto-fixable issues
npm run lint:fix

# Check specific files
npx eslint src/components/**/*.tsx
```

#### **TypeScript Checking**
```bash
# Type checking
npx tsc --noEmit

# Watch mode
npx tsc --noEmit --watch
```

## 🚀 Deployment Development

### **Local Production Build**
```bash
# Build untuk production
npm run build

# Start production server
npm start

# Analyze bundle size
npm run analyze
```

### **Environment-specific Builds**
```bash
# Development build
NODE_ENV=development npm run build

# Production build
NODE_ENV=production npm run build

# Test production build locally
npm run build && npm start
```

### **Vercel Deployment**
```bash
# Install Vercel CLI
npm i -g vercel

# Login ke Vercel
vercel login

# Deploy ke preview
vercel

# Deploy ke production
vercel --prod
```

## 🐛 Debugging & Troubleshooting

### **Common Issues & Solutions**

#### **Database Connection Issues**
```bash
# Check Docker containers
docker ps

# Restart database
docker-compose down
docker-compose up -d

# Reset database
npx prisma db push --force-reset
npm run db:seed
```

#### **Port Already in Use**
```bash
# Kill process on port 3000
npx kill-port 3000

# Or use different port
npm run dev -- -p 3001
```

#### **TypeScript Errors**
```bash
# Clear TypeScript cache
rm -rf .next
rm -rf node_modules/.cache

# Reinstall dependencies
rm -rf node_modules package-lock.json
npm install
```

#### **Prisma Issues**
```bash
# Regenerate Prisma client
npx prisma generate

# Reset Prisma
npx prisma migrate reset
npx prisma db push
```

### **Development Debugging**

#### **API Debugging**
```typescript
// Add logging ke API routes
console.log('Request body:', await request.json())
console.log('User:', user)
console.log('Database result:', result)
```

#### **Frontend Debugging**
```typescript
// React DevTools
// Install React Developer Tools browser extension

// Context debugging
const { user, loading } = useAuth()
console.log('Auth state:', { user, loading })
```

#### **Database Debugging**
```bash
# Enable Prisma query logging
# Add ke .env.local
DATABASE_URL="postgresql://postgres:postgres@localhost:5432/mybinder?schema=public&logging=true"
```

## 📚 Development Resources

### **Documentation Links**
- **Next.js**: https://nextjs.org/docs
- **Prisma**: https://www.prisma.io/docs
- **Tailwind CSS**: https://tailwindcss.com/docs
- **TypeScript**: https://www.typescriptlang.org/docs

### **Project-specific Documentation**
- **API Documentation**: `ignore-this/API.md`
- **Database Schema**: `prisma/schema.prisma`
- **Component Documentation**: Inline JSDoc comments
- **Testing Guide**: `jest.config.js` dan test files

### **Development Commands Reference**
```bash
# Development
npm run dev              # Start dev server
npm run build           # Build for production
npm run start           # Start production server

# Database
npm run db:seed         # Seed database
npm run db:reset        # Reset database
npx prisma studio       # Open Prisma Studio

# Testing
npm test                # Run unit tests
npm run test:watch      # Run tests in watch mode
npm run test:coverage   # Run tests with coverage
npx playwright test     # Run E2E tests

# Code Quality
npm run lint            # Run ESLint
npm run lint:fix        # Fix ESLint errors
npx tsc --noEmit        # Type checking
```

Panduan ini memberikan semua informasi yang dibutuhkan untuk setup dan development yang efisien pada proyek MyBinder.
