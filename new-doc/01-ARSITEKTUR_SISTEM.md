# Arsitektur Sistem MyBinder

## 🏗️ Gambaran Umum Arsitektur

MyBinder dibangun menggunakan arsitektur **Full-Stack Modern** dengan pendekatan **Monolithic Frontend + API Backend** yang terintegrasi dalam satu aplikasi Next.js.

### Diagram Arsitektur Tingkat Tinggi

```mermaid
graph TB
    subgraph "Client Layer"
        A[Web Browser]
        B[Mobile Browser]
    end
    
    subgraph "Frontend Layer (Next.js)"
        C[React Components]
        D[Context Providers]
        E[Custom Hooks]
        F[Tailwind CSS]
    end
    
    subgraph "API Layer (Next.js API Routes)"
        G[Authentication API]
        H[Groups API]
        I[Messages API]
        J[Notes API]
        K[Blocks API]
    end
    
    subgraph "Business Logic Layer"
        L[Middleware]
        M[Validation (Zod)]
        N[Authorization]
        O[Error Handling]
    end
    
    subgraph "Data Layer"
        P[Prisma ORM]
        Q[PostgreSQL Database]
    end
    
    subgraph "External Services"
        R[Vercel Deployment]
        S[Supabase Database]
    end
    
    A --> C
    B --> C
    C --> D
    D --> E
    C --> G
    C --> H
    C --> I
    C --> J
    G --> L
    H --> L
    I --> L
    J --> L
    L --> M
    L --> N
    L --> P
    P --> Q
    R --> S
```

## 🔧 Komponen Utama Sistem

### 1. Frontend Layer (Client-Side)

#### **React Components**
- **Modular Component Structure**: Setiap fitur memiliki komponen terpisah
- **Responsive Design**: Mendukung desktop dan mobile dengan Tailwind CSS
- **Component Hierarchy**:
  ```
  App Layout
  ├── Authentication Components
  ├── Group Management Components
  ├── Chat Interface Components
  ├── Notes Editor Components
  └── Common UI Components
  ```

#### **State Management dengan Context API**
- **AuthContext**: Mengelola state autentikasi pengguna
- **GroupContext**: Mengelola state grup dan membership
- **MessageContext**: Mengelola state pesan dan chat
- **NotesContext**: Mengelola state catatan dan blok

#### **Custom Hooks**
- **useIsMobile**: Deteksi perangkat mobile
- **useSwipeGesture**: Gesture handling untuk mobile
- **useSmartPosition**: Positioning untuk UI elements

### 2. API Layer (Server-Side)

#### **Next.js API Routes Structure**
```
/api
├── /auth
│   ├── /login
│   ├── /register
│   ├── /logout
│   └── /me
├── /groups
│   ├── /[groupId]
│   │   ├── /messages
│   │   ├── /notes
│   │   ├── /join
│   │   ├── /leave
│   │   └── /transfer
│   └── /discover
├── /messages
│   └── /[messageId]
├── /notes
│   └── /[noteId]
└── /blocks
    └── /[blockId]
```

#### **API Design Patterns**
- **RESTful API**: Menggunakan HTTP methods yang sesuai (GET, POST, PUT, DELETE)
- **Consistent Response Format**: Semua API mengembalikan format JSON yang konsisten
- **Error Handling**: Centralized error handling dengan status codes yang tepat

### 3. Business Logic Layer

#### **Middleware System**
- **Authentication Middleware**: Verifikasi JWT token
- **Authorization Middleware**: Role-based access control
- **Validation Middleware**: Input validation menggunakan Zod

#### **Security Implementation**
- **JWT Authentication**: Token-based authentication dengan HTTP-only cookies
- **Password Hashing**: bcrypt untuk enkripsi password
- **Role-based Authorization**: Owner, Admin, Member dengan hak akses berbeda

### 4. Data Layer

#### **Database Schema (PostgreSQL + Prisma)**
```mermaid
erDiagram
    User ||--o{ GroupMember : has
    User ||--o{ Group : owns
    User ||--o{ Message : sends
    User ||--o{ Note : creates
    User ||--o{ NoteBlock : authors
    
    Group ||--o{ GroupMember : contains
    Group ||--o{ Message : has
    Group ||--o{ Note : contains
    
    Message ||--o{ Message : replies
    
    Note ||--o{ NoteBlock : contains
    
    User {
        string id PK
        string email UK
        string username UK
        string password
        string name
        string avatar
        datetime createdAt
        datetime updatedAt
    }
    
    Group {
        string id PK
        string name
        string description
        string avatar
        boolean isPrivate
        string ownerId FK
        datetime createdAt
        datetime updatedAt
    }
    
    GroupMember {
        string id PK
        enum role
        string userId FK
        string groupId FK
        datetime joinedAt
    }
    
    Message {
        string id PK
        string content
        string authorId FK
        string groupId FK
        string parentMessageId FK
        int threadDepth
        datetime createdAt
        datetime updatedAt
    }
    
    Note {
        string id PK
        string title
        string description
        string authorId FK
        string groupId FK
        datetime createdAt
        datetime updatedAt
    }
    
    NoteBlock {
        string id PK
        enum type
        string content
        int order
        string noteId FK
        string authorId FK
        datetime createdAt
        datetime updatedAt
    }
```

## 🔄 Flow Data dan Interaksi

### 1. Authentication Flow
```mermaid
sequenceDiagram
    participant U as User
    participant F as Frontend
    participant A as Auth API
    participant D as Database
    
    U->>F: Login Request
    F->>A: POST /api/auth/login
    A->>D: Verify Credentials
    D-->>A: User Data
    A->>A: Generate JWT
    A-->>F: JWT + User Data
    F->>F: Store User State
    F-->>U: Redirect to Dashboard
```

### 2. Real-time Messaging Flow
```mermaid
sequenceDiagram
    participant U1 as User 1
    participant F1 as Frontend 1
    participant API as Messages API
    participant DB as Database
    participant F2 as Frontend 2
    participant U2 as User 2
    
    U1->>F1: Send Message
    F1->>API: POST /api/groups/{id}/messages
    API->>DB: Store Message
    DB-->>API: Message Data
    API-->>F1: Success Response
    F1->>F1: Update Local State
    F1-->>U1: Show Message
    
    Note over F2: Polling for new messages
    F2->>API: GET /api/groups/{id}/messages
    API->>DB: Fetch Messages
    DB-->>API: Messages Data
    API-->>F2: Messages Response
    F2->>F2: Update State
    F2-->>U2: Show New Message
```

### 3. Notes Collaboration Flow
```mermaid
sequenceDiagram
    participant U as User
    participant F as Frontend
    participant N as Notes API
    participant B as Blocks API
    participant D as Database
    
    U->>F: Edit Note Block
    F->>B: PUT /api/blocks/{id}
    B->>D: Update Block
    D-->>B: Updated Block
    B-->>F: Success Response
    F->>F: Update Local State
    F-->>U: Show Updated Content
```

## 📊 Karakteristik Arsitektur

### **Kelebihan Arsitektur Saat Ini**

1. **Simplicity**: Monolithic structure mudah di-develop dan deploy
2. **Performance**: Server-side rendering dengan Next.js
3. **Type Safety**: Full TypeScript implementation
4. **Scalability**: Dapat di-scale horizontal di Vercel
5. **Developer Experience**: Hot reload, TypeScript, modern tooling

### **Pertimbangan Scalability**

1. **Database Scaling**: PostgreSQL dengan connection pooling
2. **API Rate Limiting**: Belum diimplementasikan (future enhancement)
3. **Caching Strategy**: Belum ada caching layer (future enhancement)
4. **Real-time Features**: Saat ini menggunakan polling (bisa ditingkatkan dengan WebSocket)

### **Security Considerations**

1. **Authentication**: JWT dengan HTTP-only cookies
2. **Authorization**: Role-based access control
3. **Input Validation**: Zod validation di semua endpoints
4. **SQL Injection Prevention**: Prisma ORM dengan parameterized queries
5. **XSS Prevention**: React built-in protection + input sanitization

## 🚀 Deployment Architecture

### **Production Environment**
```mermaid
graph LR
    subgraph "Vercel Platform"
        A[Next.js App]
        B[API Routes]
        C[Static Assets]
    end
    
    subgraph "Supabase"
        D[PostgreSQL]
        E[Connection Pooling]
    end
    
    F[CDN] --> A
    A --> B
    B --> D
    D --> E
```

### **Development Environment**
```mermaid
graph LR
    subgraph "Local Development"
        A[Next.js Dev Server]
        B[Hot Reload]
    end
    
    subgraph "Docker Container"
        C[PostgreSQL]
        D[pgAdmin]
    end
    
    A --> C
    B --> A
```

## 🔮 Future Architecture Considerations

### **Potential Enhancements**

1. **Real-time Communication**
   - Implementasi WebSocket/Socket.IO untuk real-time messaging
   - Server-Sent Events untuk notifications

2. **Microservices Migration**
   - Pemisahan service untuk scaling yang lebih baik
   - API Gateway untuk service orchestration

3. **Caching Layer**
   - Redis untuk session storage
   - Application-level caching untuk performance

4. **Message Queue**
   - Background job processing
   - Email notifications
   - File upload processing

Arsitektur saat ini sudah solid untuk MVP dan dapat di-scale sesuai kebutuhan bisnis yang berkembang.
