import { PrismaClient } from '@prisma/client'
import { hashPassword } from '../src/lib/auth'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Seeding database...')

  // Clear existing data in correct order (respecting foreign key constraints)
  console.log('🧹 Clearing existing data...')
  await prisma.noteBlock.deleteMany()
  await prisma.note.deleteMany()
  await prisma.message.deleteMany()
  await prisma.groupMember.deleteMany()
  await prisma.group.deleteMany()
  await prisma.user.deleteMany()

  // Create demo users with different roles
  console.log('👥 Creating demo users...')
  const hashedPassword = await hashPassword('demo123')

  // Owner users (will own groups)
  const ownerUser1 = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      username: 'owner1',
      password: hashedPassword,
      name: '<PERSON> (Owner)',
    },
  })

  const ownerUser2 = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      username: 'owner2',
      password: hashedPassword,
      name: '<PERSON> (Owner)',
    },
  })

  // Admin users (will have admin roles in groups)
  const adminUser1 = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      username: 'admin1',
      password: hashedPassword,
      name: 'Emma Rodriguez (Admin)',
    },
  })

  const adminUser2 = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      username: 'admin2',
      password: hashedPassword,
      name: 'David Kim (Admin)',
    },
  })

  // Regular member users
  const memberUser1 = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      username: 'member1',
      password: hashedPassword,
      name: 'Lisa Thompson (Member)',
    },
  })

  const memberUser2 = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      username: 'member2',
      password: hashedPassword,
      name: 'James Wilson (Member)',
    },
  })

  const memberUser3 = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      username: 'member3',
      password: hashedPassword,
      name: 'Anna Garcia (Member)',
    },
  })

  // Additional test user for join/leave testing
  const testUser = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      username: 'testuser',
      password: hashedPassword,
      name: 'Test User',
    },
  })

  // Create demo groups with different privacy settings
  console.log('🏢 Creating demo groups...')

  // Public groups
  const publicGroup1 = await prisma.group.create({
    data: {
      name: 'General Discussion',
      description: 'A place for general conversations and announcements',
      ownerId: ownerUser1.id,
      isPrivate: false,
    },
  })

  const publicGroup2 = await prisma.group.create({
    data: {
      name: 'Project Planning',
      description: 'Collaborate on project ideas and planning',
      ownerId: ownerUser1.id,
      isPrivate: false,
    },
  })

  const publicGroup3 = await prisma.group.create({
    data: {
      name: 'Tech Discussions',
      description: 'Share and discuss the latest in technology',
      ownerId: ownerUser2.id,
      isPrivate: false,
    },
  })

  // Private groups
  const privateGroup1 = await prisma.group.create({
    data: {
      name: 'Leadership Team',
      description: 'Private discussions for leadership team members',
      ownerId: ownerUser1.id,
      isPrivate: true,
    },
  })

  const privateGroup2 = await prisma.group.create({
    data: {
      name: 'VIP Members',
      description: 'Exclusive group for VIP members only',
      ownerId: ownerUser2.id,
      isPrivate: true,
    },
  })

  // Small group for testing transfer ownership
  const smallGroup = await prisma.group.create({
    data: {
      name: 'Small Team',
      description: 'A small team for testing ownership transfer',
      ownerId: memberUser1.id,
      isPrivate: false,
    },
  })

  // Create GroupMember records with proper role hierarchy
  console.log('👤 Creating group memberships with roles...')

  await prisma.groupMember.createMany({
    data: [
      // General Discussion Group (Public) - Owner: ownerUser1
      { userId: ownerUser1.id, groupId: publicGroup1.id, role: 'OWNER' },
      { userId: adminUser1.id, groupId: publicGroup1.id, role: 'ADMIN' },
      { userId: adminUser2.id, groupId: publicGroup1.id, role: 'ADMIN' },
      { userId: memberUser1.id, groupId: publicGroup1.id, role: 'MEMBER' },
      { userId: memberUser2.id, groupId: publicGroup1.id, role: 'MEMBER' },

      // Project Planning Group (Public) - Owner: ownerUser1
      { userId: ownerUser1.id, groupId: publicGroup2.id, role: 'OWNER' },
      { userId: adminUser1.id, groupId: publicGroup2.id, role: 'ADMIN' },
      { userId: memberUser1.id, groupId: publicGroup2.id, role: 'MEMBER' },
      { userId: memberUser3.id, groupId: publicGroup2.id, role: 'MEMBER' },

      // Tech Discussions Group (Public) - Owner: ownerUser2
      { userId: ownerUser2.id, groupId: publicGroup3.id, role: 'OWNER' },
      { userId: adminUser2.id, groupId: publicGroup3.id, role: 'ADMIN' },
      { userId: memberUser2.id, groupId: publicGroup3.id, role: 'MEMBER' },
      { userId: memberUser3.id, groupId: publicGroup3.id, role: 'MEMBER' },

      // Leadership Team (Private) - Owner: ownerUser1
      { userId: ownerUser1.id, groupId: privateGroup1.id, role: 'OWNER' },
      { userId: ownerUser2.id, groupId: privateGroup1.id, role: 'ADMIN' },
      { userId: adminUser1.id, groupId: privateGroup1.id, role: 'ADMIN' },

      // VIP Members (Private) - Owner: ownerUser2
      { userId: ownerUser2.id, groupId: privateGroup2.id, role: 'OWNER' },
      { userId: adminUser2.id, groupId: privateGroup2.id, role: 'ADMIN' },
      { userId: memberUser1.id, groupId: privateGroup2.id, role: 'MEMBER' },

      // Small Team (Public) - Owner: memberUser1 (for testing ownership transfer)
      { userId: memberUser1.id, groupId: smallGroup.id, role: 'OWNER' },
      { userId: memberUser2.id, groupId: smallGroup.id, role: 'MEMBER' },
    ],
  })

  // Create demo messages across different groups
  console.log('💬 Creating demo messages...')

  await prisma.message.createMany({
    data: [
      // General Discussion Group messages
      {
        content: 'Welcome to MyBinder! This is our general discussion group where we share updates and have conversations.',
        authorId: ownerUser1.id,
        groupId: publicGroup1.id,
      },
      {
        content: 'Thanks for the welcome! Excited to be part of this community.',
        authorId: memberUser1.id,
        groupId: publicGroup1.id,
      },
      {
        content: 'As an admin, I\'ll help moderate discussions and assist with any questions.',
        authorId: adminUser1.id,
        groupId: publicGroup1.id,
      },
      {
        content: 'Looking forward to collaborating with everyone here!',
        authorId: memberUser2.id,
        groupId: publicGroup1.id,
      },
      {
        content: 'Feel free to share any ideas or feedback you have.',
        authorId: adminUser2.id,
        groupId: publicGroup1.id,
      },

      // Project Planning Group messages
      {
        content: 'Let\'s start planning our next project. What ideas do you have?',
        authorId: ownerUser1.id,
        groupId: publicGroup2.id,
      },
      {
        content: 'I think we should focus on improving user experience first.',
        authorId: adminUser1.id,
        groupId: publicGroup2.id,
      },
      {
        content: 'Great idea! I can help with the design aspects.',
        authorId: memberUser1.id,
        groupId: publicGroup2.id,
      },
      {
        content: 'I\'ll work on the technical implementation plan.',
        authorId: memberUser3.id,
        groupId: publicGroup2.id,
      },

      // Tech Discussions Group messages
      {
        content: 'Welcome to our tech discussions! Share the latest trends and insights here.',
        authorId: ownerUser2.id,
        groupId: publicGroup3.id,
      },
      {
        content: 'Has anyone tried the new React 18 features? Thoughts?',
        authorId: adminUser2.id,
        groupId: publicGroup3.id,
      },
      {
        content: 'Yes! The concurrent features are game-changing.',
        authorId: memberUser2.id,
        groupId: publicGroup3.id,
      },

      // Leadership Team (Private) messages
      {
        content: 'Leadership team meeting scheduled for next week. Please review the agenda.',
        authorId: ownerUser1.id,
        groupId: privateGroup1.id,
      },
      {
        content: 'Agenda looks good. I\'ll prepare the quarterly reports.',
        authorId: adminUser1.id,
        groupId: privateGroup1.id,
      },

      // Small Team messages
      {
        content: 'This is our small team workspace. Let\'s keep it focused and productive.',
        authorId: memberUser1.id,
        groupId: smallGroup.id,
      },
    ],
  })

  // Create demo notes across different groups
  console.log('📝 Creating demo notes...')

  const note1 = await prisma.note.create({
    data: {
      title: 'Meeting Notes - Project Kickoff',
      description: 'Notes from our project kickoff meeting',
      authorId: ownerUser1.id,
      groupId: publicGroup2.id,
    },
  })

  const note2 = await prisma.note.create({
    data: {
      title: 'Ideas and Brainstorming',
      description: 'Collection of ideas for future projects',
      authorId: adminUser1.id,
      groupId: publicGroup1.id,
    },
  })

  const note3 = await prisma.note.create({
    data: {
      title: 'Tech Stack Recommendations',
      description: 'Recommended technologies for our next project',
      authorId: adminUser2.id,
      groupId: publicGroup3.id,
    },
  })

  const note4 = await prisma.note.create({
    data: {
      title: 'Team Guidelines',
      description: 'Guidelines and best practices for our small team',
      authorId: memberUser1.id,
      groupId: smallGroup.id,
    },
  })

  const note5 = await prisma.note.create({
    data: {
      title: 'Leadership Strategy',
      description: 'Strategic planning for leadership team',
      authorId: ownerUser1.id,
      groupId: privateGroup1.id,
    },
  })

  // Create demo note blocks for comprehensive testing
  console.log('📄 Creating note blocks...')

  await prisma.noteBlock.createMany({
    data: [
      // Note 1: Project Kickoff Meeting
      {
        type: 'HEADING_1',
        content: 'Project Kickoff Meeting',
        order: 0,
        noteId: note1.id,
        authorId: ownerUser1.id,
      },
      {
        type: 'TEXT',
        content: 'Date: Today\nAttendees: Sarah Johnson, Emma Rodriguez, Lisa Thompson, Anna Garcia',
        order: 1,
        noteId: note1.id,
        authorId: ownerUser1.id,
      },
      {
        type: 'HEADING_2',
        content: 'Agenda',
        order: 2,
        noteId: note1.id,
        authorId: ownerUser1.id,
      },
      {
        type: 'BULLET_LIST',
        content: '• Project overview and objectives\n• Timeline and milestones\n• Resource allocation\n• Role assignments\n• Next steps and action items',
        order: 3,
        noteId: note1.id,
        authorId: ownerUser1.id,
      },

      // Note 2: Ideas and Brainstorming
      {
        type: 'HEADING_1',
        content: 'Brainstorming Session',
        order: 0,
        noteId: note2.id,
        authorId: adminUser1.id,
      },
      {
        type: 'TEXT',
        content: 'Here are some innovative ideas we discussed during our brainstorming session:',
        order: 1,
        noteId: note2.id,
        authorId: adminUser1.id,
      },
      {
        type: 'NUMBERED_LIST',
        content: '1. Mobile app development with React Native\n2. Web platform enhancement using Next.js\n3. User experience improvements\n4. Performance optimization\n5. AI integration features',
        order: 2,
        noteId: note2.id,
        authorId: adminUser1.id,
      },

      // Note 3: Tech Stack Recommendations
      {
        type: 'HEADING_1',
        content: 'Recommended Tech Stack',
        order: 0,
        noteId: note3.id,
        authorId: adminUser2.id,
      },
      {
        type: 'HEADING_2',
        content: 'Frontend Technologies',
        order: 1,
        noteId: note3.id,
        authorId: adminUser2.id,
      },
      {
        type: 'BULLET_LIST',
        content: '• React 18 with TypeScript\n• Next.js 15 for SSR/SSG\n• Tailwind CSS for styling\n• Framer Motion for animations',
        order: 2,
        noteId: note3.id,
        authorId: adminUser2.id,
      },
      {
        type: 'HEADING_2',
        content: 'Backend Technologies',
        order: 3,
        noteId: note3.id,
        authorId: adminUser2.id,
      },
      {
        type: 'BULLET_LIST',
        content: '• Node.js with Express\n• PostgreSQL database\n• Prisma ORM\n• JWT authentication',
        order: 4,
        noteId: note3.id,
        authorId: adminUser2.id,
      },

      // Note 4: Team Guidelines
      {
        type: 'HEADING_1',
        content: 'Team Guidelines',
        order: 0,
        noteId: note4.id,
        authorId: memberUser1.id,
      },
      {
        type: 'TEXT',
        content: 'These guidelines will help us work effectively as a small, focused team.',
        order: 1,
        noteId: note4.id,
        authorId: memberUser1.id,
      },
      {
        type: 'NUMBERED_LIST',
        content: '1. Daily standup meetings at 9 AM\n2. Code reviews required for all PRs\n3. Documentation for all new features\n4. Weekly retrospectives on Fridays',
        order: 2,
        noteId: note4.id,
        authorId: memberUser1.id,
      },

      // Note 5: Leadership Strategy
      {
        type: 'HEADING_1',
        content: 'Leadership Strategy 2024',
        order: 0,
        noteId: note5.id,
        authorId: ownerUser1.id,
      },
      {
        type: 'TEXT',
        content: 'Strategic initiatives for the leadership team this year.',
        order: 1,
        noteId: note5.id,
        authorId: ownerUser1.id,
      },
      {
        type: 'BULLET_LIST',
        content: '• Team growth and development\n• Process optimization\n• Technology roadmap\n• Performance metrics',
        order: 2,
        noteId: note5.id,
        authorId: ownerUser1.id,
      },
    ],
  })

  console.log('✅ Database seeded successfully!')
  console.log('')
  console.log('🎯 Demo Accounts for Testing Group Management Features:')
  console.log('')
  console.log('👑 OWNER ACCOUNTS:')
  console.log('- Email: <EMAIL>, Password: demo123 (Sarah Johnson)')
  console.log('  • Owns: General Discussion, Project Planning, Leadership Team')
  console.log('- Email: <EMAIL>, Password: demo123 (Michael Chen)')
  console.log('  • Owns: Tech Discussions, VIP Members')
  console.log('')
  console.log('🛡️ ADMIN ACCOUNTS:')
  console.log('- Email: <EMAIL>, Password: demo123 (Emma Rodriguez)')
  console.log('  • Admin in: General Discussion, Project Planning, Leadership Team')
  console.log('- Email: <EMAIL>, Password: demo123 (David Kim)')
  console.log('  • Admin in: General Discussion, Tech Discussions, VIP Members')
  console.log('')
  console.log('👥 MEMBER ACCOUNTS:')
  console.log('- Email: <EMAIL>, Password: demo123 (Lisa Thompson)')
  console.log('  • Member in: General Discussion, Project Planning, VIP Members')
  console.log('  • Owner of: Small Team (for testing ownership transfer)')
  console.log('- Email: <EMAIL>, Password: demo123 (James Wilson)')
  console.log('  • Member in: General Discussion, Tech Discussions, Small Team')
  console.log('- Email: <EMAIL>, Password: demo123 (Anna Garcia)')
  console.log('  • Member in: Project Planning, Tech Discussions')
  console.log('')
  console.log('🧪 TEST ACCOUNT:')
  console.log('- Email: <EMAIL>, Password: demo123 (Test User)')
  console.log('  • Not in any groups (for testing join functionality)')
  console.log('')
  console.log('📊 GROUP SUMMARY:')
  console.log('🌐 Public Groups:')
  console.log('  • General Discussion (5 members: 1 Owner, 2 Admins, 2 Members)')
  console.log('  • Project Planning (4 members: 1 Owner, 1 Admin, 2 Members)')
  console.log('  • Tech Discussions (4 members: 1 Owner, 1 Admin, 2 Members)')
  console.log('  • Small Team (2 members: 1 Owner, 1 Member)')
  console.log('🔒 Private Groups:')
  console.log('  • Leadership Team (3 members: 1 Owner, 2 Admins)')
  console.log('  • VIP Members (3 members: 1 Owner, 1 Admin, 1 Member)')
  console.log('')
  console.log('🎮 Testing Scenarios Available:')
  console.log('✅ Role-based permissions (Owner/Admin/Member)')
  console.log('✅ Join public groups (use <EMAIL>)')
  console.log('✅ Leave groups (various roles)')
  console.log('✅ Transfer ownership (Small Team: member1 → member2)')
  console.log('✅ Promote/demote members (Owners and Admins)')
  console.log('✅ Remove members (permission-based)')
  console.log('✅ Delete groups (Owner-only)')
  console.log('✅ Public vs Private group discovery')
  console.log('✅ Comprehensive role hierarchy testing')
}

main()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
