import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import '@testing-library/jest-dom'
import LoginForm from '@/components/auth/LoginForm'
import { AuthProvider } from '@/contexts/AuthContext'

// Mock the AuthContext
const mockLogin = jest.fn()
const mockAuthContext = {
  user: null,
  loading: false,
  login: mockLogin,
  register: jest.fn(),
  logout: jest.fn(),
  refreshUser: jest.fn(),
}

jest.mock('@/contexts/AuthContext', () => ({
  AuthProvider: ({ children }: { children: React.ReactNode }) => children,
  useAuth: () => mockAuthContext,
}))

describe('LoginForm', () => {
  beforeEach(() => {
    mockLogin.mockClear()
  })

  it('renders login form correctly', () => {
    render(<LoginForm />)
    
    expect(screen.getByLabelText(/email/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/password/i)).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /sign in/i })).toBeInTheDocument()
  })

  it('shows demo account information', () => {
    render(<LoginForm />)

    expect(screen.getAllByText(/demo accounts/i)[0]).toBeInTheDocument()
    expect(screen.getByText(/Sarah Johnson/i)).toBeInTheDocument()
    expect(screen.getByText(/Emma Rodriguez/i)).toBeInTheDocument()
    expect(screen.getByText(/Lisa Thompson/i)).toBeInTheDocument()
    expect(screen.getByText(/Test User/i)).toBeInTheDocument()
  })

  it('handles form submission with valid data', async () => {
    const user = userEvent.setup()
    mockLogin.mockResolvedValue(undefined)
    
    render(<LoginForm />)
    
    const emailInput = screen.getByLabelText(/email/i)
    const passwordInput = screen.getByLabelText(/password/i)
    const submitButton = screen.getByRole('button', { name: /sign in/i })
    
    await user.type(emailInput, '<EMAIL>')
    await user.type(passwordInput, 'password123')
    await user.click(submitButton)
    
    await waitFor(() => {
      expect(mockLogin).toHaveBeenCalledWith('<EMAIL>', 'password123')
    })
  })

  it('shows error message on login failure', async () => {
    const user = userEvent.setup()
    const errorMessage = 'Invalid credentials'
    mockLogin.mockRejectedValue(new Error(errorMessage))
    
    render(<LoginForm />)
    
    const emailInput = screen.getByLabelText(/email/i)
    const passwordInput = screen.getByLabelText(/password/i)
    const submitButton = screen.getByRole('button', { name: /sign in/i })
    
    await user.type(emailInput, '<EMAIL>')
    await user.type(passwordInput, 'wrongpassword')
    await user.click(submitButton)
    
    await waitFor(() => {
      expect(screen.getByText(errorMessage)).toBeInTheDocument()
    })
  })

  it('disables submit button when loading', async () => {
    const user = userEvent.setup()
    mockAuthContext.login = jest.fn().mockImplementation(() => new Promise(() => {})) // Never resolves

    render(<LoginForm />)

    // Fill form and submit to trigger loading state
    await user.type(screen.getByLabelText(/email/i), '<EMAIL>')
    await user.type(screen.getByLabelText(/password/i), 'password')
    await user.click(screen.getByRole('button', { name: /sign in/i }))

    const submitButton = screen.getByRole('button', { name: /signing in/i })
    expect(submitButton).toBeDisabled()
  })

  it('validates required fields', async () => {
    const user = userEvent.setup()
    
    render(<LoginForm />)
    
    const submitButton = screen.getByRole('button', { name: /sign in/i })
    await user.click(submitButton)
    
    // Form should not submit without required fields
    expect(mockLogin).not.toHaveBeenCalled()
  })

  it('shows demo account credentials in info section', () => {
    render(<LoginForm />)

    // Check for demo user names and roles
    expect(screen.getByText(/Sarah Johnson/i)).toBeInTheDocument()
    expect(screen.getByText(/Owner/i)).toBeInTheDocument()
    expect(screen.getByText(/Emma Rodriguez/i)).toBeInTheDocument()
    expect(screen.getByText(/Admin/i)).toBeInTheDocument()
    expect(screen.getByText(/Lisa Thompson/i)).toBeInTheDocument()
    expect(screen.getByText(/Member/i)).toBeInTheDocument()
  })
})
